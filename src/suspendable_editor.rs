use reedline::{
    ColumnarMenu, CursorConfig, EditCommand, ExternalPrinter, FileBackedHistory, Hinter, Keybindings, ListMenu,
    MenuBuilder, Prompt, Reedline, ReedlineError, ReedlineEvent, ReedlineMenu, Signal, KeyCode, KeyModifiers,
};
use std::io;
use std::path::PathBuf;
use std::process::Command;
use std::sync::{Arc, Mutex};
use thiserror::Error;
use crate::app::App;

const INTERNAL_SUSPEND_MARKER: &str = ":::SUSPENDABLE_REEDLINE_SUSPEND:::";
const INTERNAL_QUIT_MARKER: &str = ":::SUSPENDABLE_REEDLINE_QUIT:::";
const INTERNAL_INTERRUPT_MARKER: &str = ":::SUSPENDABLE_REEDLINE_INTERRUPT:::";

#[derive(<PERSON><PERSON><PERSON>, Debug)]
pub enum SuspendableError {
    #[error("Reedline error: {0}")]
    Reedline(#[from] ReedlineError),

    #[error("Failed during suspend operation: {0}")]
    Suspend(#[from] io::Error),
}

pub type SuspendableResult<T> = Result<T, SuspendableError>;

pub struct SuspendableReedline {
    pub editor: Reedline,
    suspend_marker: String,
    quit_marker: String,
    interrupt_marker: String,
    app_arc: Arc<Mutex<App>>,
}

#[derive(Debug)]
pub enum ReadResult {
    Success(String),
    ClearBuffer,
    ShouldQuit,
    InterruptAI,
}

impl SuspendableReedline {
    pub fn create_with_history_and_hinter<P: Into<PathBuf>>(
        history_path: P,
        hinter: Box<dyn Hinter>,
        app_arc: Arc<Mutex<App>>,
        external_printer: ExternalPrinter<String>,
    ) -> Self {
        let mut keybindings = reedline::default_emacs_keybindings();
        let suspend_marker = INTERNAL_SUSPEND_MARKER.to_string();
        let quit_marker = INTERNAL_QUIT_MARKER.to_string();
        let interrupt_marker = INTERNAL_INTERRUPT_MARKER.to_string();
        let history_file_path = history_path.into();

        // Configure Ctrl-C for clearing buffer or quitting
        let ctrl_c_modifier = KeyModifiers::CONTROL;
        let ctrl_c_keycode_lower = KeyCode::Char('c');
        let ctrl_c_keycode_upper = KeyCode::Char('C');
        keybindings.remove_binding(ctrl_c_modifier, ctrl_c_keycode_lower);
        keybindings.remove_binding(ctrl_c_modifier, ctrl_c_keycode_upper);
        let suspend_event = ReedlineEvent::ExecuteHostCommand(suspend_marker.clone());
        keybindings.add_binding(ctrl_c_modifier, ctrl_c_keycode_lower, suspend_event.clone());
        keybindings.add_binding(ctrl_c_modifier, ctrl_c_keycode_upper, suspend_event.clone());

        // Configure Ctrl-D for instant quit
        let ctrl_d_modifier = KeyModifiers::CONTROL;
        let ctrl_d_keycode_lower = KeyCode::Char('d');
        let ctrl_d_keycode_upper = KeyCode::Char('D');

        // Remove default Ctrl-D binding
        keybindings.remove_binding(ctrl_d_modifier, ctrl_d_keycode_lower);
        keybindings.remove_binding(ctrl_d_modifier, ctrl_d_keycode_upper);

        // Use ExecuteHostCommand with quit_marker for instant quit
        let quit_event = ReedlineEvent::ExecuteHostCommand(quit_marker.clone());
        keybindings.add_binding(ctrl_d_modifier, ctrl_d_keycode_lower, quit_event.clone());
        keybindings.add_binding(ctrl_d_modifier, ctrl_d_keycode_upper, quit_event);

        // Add menu keybindings
        Self::add_menu_keybindings(&mut keybindings);

        // Add newline keybindings
        Self::add_newline_keybinding(&mut keybindings);

        // Create history
        let history = match FileBackedHistory::with_file(500, history_file_path.clone()) {
            Ok(h) => Some(Box::new(h) as Box<dyn reedline::History>),
            Err(e) => {
                eprintln!("Error creating history file: {}", e);
                None
            }
        };

        // Create cursor config
        let cursor_config = CursorConfig {
            vi_insert: Some(crossterm::cursor::SetCursorStyle::BlinkingBar),
            vi_normal: Some(crossterm::cursor::SetCursorStyle::SteadyBlock),
            emacs: None,
        };

        // Create and configure the editor
        let mut editor = Reedline::create()
            .with_edit_mode(Box::new(reedline::Emacs::new(keybindings)))
            .with_cursor_config(cursor_config)
            .with_hinter(hinter)
            .with_external_printer(external_printer)
            .use_bracketed_paste(true)
            .with_ansi_colors(true);

        if let Some(h) = history {
            editor = editor.with_history(h);
            editor = editor.with_history_exclusion_prefix(Some(" ".to_string()));
        }

        editor = editor
            .with_menu(ReedlineMenu::EngineCompleter(Box::new(
                ColumnarMenu::default().with_name("completion_menu"),
            )))
            .with_menu(ReedlineMenu::HistoryMenu(Box::new(
                ListMenu::default().with_name("history_menu"),
            )));

        SuspendableReedline {
            editor,
            suspend_marker,
            quit_marker,
            interrupt_marker,
            app_arc,
        }
    }

    pub fn read_line(&mut self, prompt: &dyn Prompt) -> SuspendableResult<ReadResult> {
        match self.editor.read_line(prompt) {
            Ok(Signal::Success(buffer)) => {
                if buffer == self.suspend_marker {
                    let current_buffer = self.editor.current_buffer_contents().to_string();
                    if current_buffer.trim().is_empty() {
                        Ok(ReadResult::ShouldQuit)
                    } else {
                        Ok(ReadResult::ClearBuffer)
                    }
                } else if buffer == self.quit_marker {
                    Ok(ReadResult::ShouldQuit)
                } else {
                    Ok(ReadResult::Success(buffer))
                }
            }
            Ok(Signal::CtrlD) | Ok(Signal::CtrlC) => Ok(ReadResult::ShouldQuit),
            Err(e) => Err(e.into()),
        }
    }

    fn add_menu_keybindings(keybindings: &mut Keybindings) {
        keybindings.add_binding(
            KeyModifiers::CONTROL,
            KeyCode::Char('r'),
            ReedlineEvent::UntilFound(vec![
                ReedlineEvent::Menu("history_menu".to_string()),
                ReedlineEvent::MenuPageNext,
            ]),
        );
        keybindings.add_binding(
            KeyModifiers::NONE,
            KeyCode::Tab,
            ReedlineEvent::UntilFound(vec![
                ReedlineEvent::Menu("completion_menu".to_string()),
                ReedlineEvent::MenuNext,
                ReedlineEvent::Edit(vec![EditCommand::Complete]),
            ]),
        );
        keybindings.add_binding(
            KeyModifiers::SHIFT,
            KeyCode::BackTab,
            ReedlineEvent::MenuPrevious,
        );
    }

    fn add_newline_keybinding(keybindings: &mut Keybindings) {
        keybindings.add_binding(KeyModifiers::NONE, KeyCode::Up, ReedlineEvent::Up);
        keybindings.add_binding(KeyModifiers::NONE, KeyCode::Down, ReedlineEvent::Down);
        keybindings.add_binding(
            KeyModifiers::ALT,
            KeyCode::Enter,
            ReedlineEvent::Edit(vec![EditCommand::InsertNewline]),
        );
        keybindings.add_binding(
            KeyModifiers::CONTROL,
            KeyCode::Enter,
            ReedlineEvent::Edit(vec![EditCommand::InsertNewline]),
        );
        keybindings.add_binding(
            KeyModifiers::SHIFT,
            KeyCode::Enter,
            ReedlineEvent::Edit(vec![EditCommand::InsertNewline]),
        );
        keybindings.add_binding(
            KeyModifiers::META,
            KeyCode::Enter,
            ReedlineEvent::Edit(vec![EditCommand::InsertNewline]),
        );
        keybindings.add_binding(
            KeyModifiers::SUPER,
            KeyCode::Enter,
            ReedlineEvent::Edit(vec![EditCommand::InsertNewline]),
        );
        keybindings.add_binding(
            KeyModifiers::HYPER,
            KeyCode::Enter,
            ReedlineEvent::Edit(vec![EditCommand::InsertNewline]),
        );
    }

    pub fn with_buffer_editor(mut self, editor_command: Command, temp_file: PathBuf) -> Self {
        self.editor = self.editor.with_buffer_editor(editor_command, temp_file);
        self
    }
}
