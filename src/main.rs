mod app;
mod cli;
mod cli_prompt;
mod confetti;
mod display;
mod hammerspoon;
mod handler;
mod keyboard;
mod llm_client;
mod network;
mod session_manager;
mod suspendable_editor;
mod syntax_highlighting;
mod token_counting_hinter;
mod workspace;

use crate::keyboard::{
    disable_kitty_keyboard_for_meta_alt_modifiers, enable_kitty_keyboard_for_meta_alt_modifiers,
};
use crate::suspendable_editor::{ReadResult, SuspendableReedline};
use crate::token_counting_hinter::TokenCountingHinter;
use app::{App, AppMessage, MessageContent};
use crossterm::style::Stylize;
use nu_ansi_term::{Color, Style};
use reedline::EditCommand;
use std::env::temp_dir;
use std::io::ErrorKind;
use std::process::Command as OsCommand;
use std::sync::{Arc, Mutex};
use std::time::Duration;
use tokio_util::sync::CancellationToken;


// Structure to track the current API task
struct ApiTaskManager {
    current_task: Option<tokio::task::JoinHandle<()>>,
    cancellation_token: Option<CancellationToken>,
}

impl ApiTaskManager {
    fn new() -> Self {
        Self {
            current_task: None,
            cancellation_token: None,
        }
    }

    fn start_task(&mut self, task: tokio::task::JoinHandle<()>, token: CancellationToken) {
        // Cancel any existing task
        self.cancel_current_task();

        self.current_task = Some(task);
        self.cancellation_token = Some(token);
    }

    fn cancel_current_task(&mut self) {
        if let Some(token) = &self.cancellation_token {
            token.cancel();
        }
        if let Some(task) = &self.current_task {
            task.abort();
        }
        self.current_task = None;
        self.cancellation_token = None;
    }

    fn is_task_running(&self) -> bool {
        if let Some(task) = &self.current_task {
            !task.is_finished()
        } else {
            false
        }
    }

    fn check_and_cleanup_finished_task(&mut self) {
        if let Some(task) = &self.current_task {
            if task.is_finished() {
                self.current_task = None;
                self.cancellation_token = None;
            }
        }
    }
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let cli_args = cli::parse_cli_args();

    let request_timeout = Duration::from_secs(180);
    let http_client = network::create_client(request_timeout, &cli_args);
    let app_arc = Arc::new(Mutex::new(App::new(http_client.clone())));

    let session_dir_path = session_manager::ensure_session_dir_exists()?;
    let session_id = cli_args
        .restore
        .clone()
        .unwrap_or_else(session_manager::generate_new_session_id);
    let session_file_path = session_manager::get_session_file_path(&session_dir_path, &session_id);

    println!("\nDima AI Agent using grok-3-mini-high via Pollinations.AI");
    println!("Session ID: {}", session_id.clone().yellow());

    if cli_args.restore.is_some() || session_file_path.exists() {
        match session_manager::load_session(&session_file_path) {
            Ok(loaded_llm_history) => {
                if !loaded_llm_history.is_empty() {
                    println!("Restored session: {}", session_id.clone().green());
                    let mut constructed_app_messages = Vec::new();
                    {
                        let mut app_locked = app_arc.lock().unwrap();
                        app_locked.conversation_history_for_llm = loaded_llm_history;
                        for chat_msg in &app_locked.conversation_history_for_llm {
                            let display_msg = if chat_msg.role == "user" {
                                AppMessage {
                                    sender: "User".to_string(),
                                    parts: vec![MessageContent::Text(chat_msg.content.clone())],
                                }
                            } else if chat_msg.role == "assistant" {
                                app_locked.create_ai_app_message_from_raw(&chat_msg.content)
                            } else if chat_msg.role == "tool" {
                                AppMessage {
                                    sender: "Tool Execution".to_string(),
                                    parts: vec![MessageContent::Text(format!(
                                        "[Tool Result]\n{}",
                                        chat_msg.content.clone()
                                    ))],
                                }
                            } else {
                                continue;
                            };
                            constructed_app_messages.push(display_msg);
                        }
                    }
                    if !constructed_app_messages.is_empty() {
                        let mut app_locked = app_arc.lock().unwrap();
                        for msg in &constructed_app_messages {
                            app_locked.messages.push(msg.clone());
                        }
                    }
                    for msg_to_print in &constructed_app_messages {
                        display::print_formatted_message(msg_to_print)?;
                    }
                } else if cli_args.restore.is_some() {
                    println!(
                        "Starting new session (or session file was empty): {}",
                        session_id.clone().yellow()
                    );
                }
            }
            Err(e) if e.kind() == ErrorKind::NotFound => {
                println!(
                    "Starting new session (no existing file found for ID): {}",
                    session_id.clone().yellow()
                );
            }
            Err(e) => {
                eprintln!(
                    "Error loading session '{}': {}. Starting fresh.",
                    session_id.clone().red(),
                    e
                );
            }
        }
    } else {
        println!("Starting new session: {}", session_id.clone().yellow());
    }

    // Create shared state for token counting
    let prompt_buffer_state_arc =
        Arc::new(Mutex::new(cli_prompt::CurrentPromptBufferState::default()));
    let token_counting_prompt = cli_prompt::TokenCountingPrompt::new(app_arc.clone());

    // Create API task manager
    let mut api_task_manager = ApiTaskManager::new();

    // Create the token counting hinter that will update buffer state in real-time
    let token_counting_hinter =
        TokenCountingHinter::new(app_arc.clone(), prompt_buffer_state_arc.clone(), &cli_args)
            .with_style(Style::new().fg(Color::DarkGray));

    // Create a suspendable editor with external editor support and custom hinter
    let temp_file = temp_dir().join("rust_llm_tui_edit_buffer.tmp");
    let editor_cmd_str = std::env::var("EDITOR").unwrap_or_else(|_| "emacsclient".to_string());
    let mut editor_os_cmd = OsCommand::new(editor_cmd_str);
    editor_os_cmd.arg(&temp_file);

    // Create external printer for non-blocking readline
    let external_printer = reedline::ExternalPrinter::default();

    let mut line_editor = SuspendableReedline::create_with_history_and_hinter(
        "llm_tui_history.txt",
        Box::new(token_counting_hinter),
        app_arc.clone(),
        external_printer,
    )
    .with_buffer_editor(editor_os_cmd, temp_file);

    enable_kitty_keyboard_for_meta_alt_modifiers();

    'main_loop: loop {
        // Check if there's a running API task and clean up if finished
        api_task_manager.check_and_cleanup_finished_task();

        // The TokenCountingHinter will automatically update the buffer state on every character input
        let read_result = line_editor.read_line(&token_counting_prompt);

        match read_result {
            Ok(ReadResult::Success(buffer)) => {
                let trimmed_buffer = buffer.trim();
                if trimmed_buffer == "/exit" {
                    api_task_manager.cancel_current_task();
                    break 'main_loop;
                } else if trimmed_buffer == "/clear" {
                    println!("\nCleared all messages.\n");
                    let messages_to_reprint;
                    {
                        // Lock scope
                        let app_locked = app_arc.lock().unwrap();
                        messages_to_reprint = app_locked.messages.clone();
                    } // Lock released
                    for msg in messages_to_reprint {
                        display::print_formatted_message(&msg)?;
                    }
                    continue;
                } else if trimmed_buffer == "/help" {
                    println!("\nCtrl-D to quit. Ctrl-C to clear prompt or exit if empty. Cmd+Enter for newline.\n");
                    continue;
                } else if trimmed_buffer.is_empty() {
                    continue;
                }

                // Create cancellation token for this API call
                let cancellation_token = CancellationToken::new();

                // Set AI thinking state
                {
                    let mut app_locked = app_arc.lock().unwrap();
                    app_locked.ai_is_thinking = true;
                }

                // Spawn the API task
                let app_arc_clone = app_arc.clone();
                let http_client_clone = http_client.clone();
                let session_file_path_clone = session_file_path.clone();
                let cli_args_clone = cli_args.clone();
                let cancellation_token_clone = cancellation_token.clone();

                let task = tokio::spawn(async move {
                    let result = handler::process_user_input(
                        buffer,
                        app_arc_clone.clone(),
                        &http_client_clone,
                        &session_file_path_clone,
                        &cli_args_clone,
                        &cancellation_token_clone,
                    ).await;

                    // Clear AI thinking state when done
                    {
                        let mut app_locked = app_arc_clone.lock().unwrap();
                        app_locked.ai_is_thinking = false;
                    }

                    if let Err(e) = result {
                        eprintln!("Error processing user input: {}", e);
                    }
                });

                api_task_manager.start_task(task, cancellation_token);
            }
            Ok(ReadResult::ShouldQuit) => {
                api_task_manager.cancel_current_task();
                app_arc.lock().unwrap().should_quit = true;
            }
            Ok(ReadResult::ClearBuffer) => {
                // If AI is thinking, cancel the request
                if api_task_manager.is_task_running() {
                    api_task_manager.cancel_current_task();

                    // Clear AI thinking state
                    {
                        let mut app_locked = app_arc.lock().unwrap();
                        app_locked.ai_is_thinking = false;
                    }

                    // Add a user cancellation message to the conversation
                    let cancellation_message = AppMessage {
                        sender: "User".to_string(),
                        parts: vec![MessageContent::Text("User aborted request".to_string())],
                    };

                    {
                        let mut app_locked = app_arc.lock().unwrap();
                        app_locked.messages.push(cancellation_message.clone());
                        app_locked.add_user_message_to_llm_history("User aborted request".to_string());

                        // Save the session with the cancellation message
                        if let Err(e) = session_manager::save_session(
                            &session_file_path,
                            &app_locked.conversation_history_for_llm,
                        ) {
                            eprintln!("{}", format!("Error saving session: {}", e).red());
                        }
                    }

                    // Display the cancellation message
                    display::print_formatted_message(&cancellation_message)?;
                } else {
                    // Normal clear buffer behavior
                    line_editor.editor.run_edit_commands(&[EditCommand::Clear]);
                }
            }
            Ok(ReadResult::InterruptAI) => {
                // Handle AI interruption (same as ClearBuffer when AI is thinking)
                if api_task_manager.is_task_running() {
                    api_task_manager.cancel_current_task();

                    // Clear AI thinking state
                    {
                        let mut app_locked = app_arc.lock().unwrap();
                        app_locked.ai_is_thinking = false;
                    }

                    // Add a user cancellation message to the conversation
                    let cancellation_message = AppMessage {
                        sender: "User".to_string(),
                        parts: vec![MessageContent::Text("User aborted request".to_string())],
                    };

                    {
                        let mut app_locked = app_arc.lock().unwrap();
                        app_locked.messages.push(cancellation_message.clone());
                        app_locked.add_user_message_to_llm_history("User aborted request".to_string());

                        // Save the session with the cancellation message
                        if let Err(e) = session_manager::save_session(
                            &session_file_path,
                            &app_locked.conversation_history_for_llm,
                        ) {
                            eprintln!("{}", format!("Error saving session: {}", e).red());
                        }
                    }

                    // Display the cancellation message
                    display::print_formatted_message(&cancellation_message)?;
                }
            }
            Err(err) => {
                eprintln!("SuspendableReedline error: {:?}. Exiting.", err);
                api_task_manager.cancel_current_task();
                app_arc.lock().unwrap().should_quit = true;
            }
        }

        if app_arc.lock().unwrap().should_quit {
            break 'main_loop;
        }
    }

    disable_kitty_keyboard_for_meta_alt_modifiers();
    Ok(())
}
